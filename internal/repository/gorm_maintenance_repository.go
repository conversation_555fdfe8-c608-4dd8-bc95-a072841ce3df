package repository

import (
	"fmt"
	"time"

	"tradicao/internal/database"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormMaintenanceRepository implementa a interface MaintenanceRepository usando GORM
type GormMaintenanceRepository struct {
	db *gorm.DB
}

// NewGormMaintenanceRepository cria uma nova instância do repositório
func NewGormMaintenanceRepository(db *gorm.DB) *GormMaintenanceRepository {
	if db == nil {
		db = database.GetGormDB()
		if db == nil {
			var err error
			db, err = database.InitGorm()
			if err != nil {
				return nil
			}
		}
	}
	return &GormMaintenanceRepository{db: db}
}

// Create implementa MaintenanceRepository.Create
func (r *GormMaintenanceRepository) Create(order *models.MaintenanceOrder) error {
	return r.db.Create(order).Error
}

// GetByID implementa MaintenanceRepository.GetByID
func (r *GormMaintenanceRepository) GetByID(id uint) (*models.MaintenanceOrder, error) {
	var order models.MaintenanceOrder
	if err := r.db.First(&order, id).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// Update implementa MaintenanceRepository.Update
func (r *GormMaintenanceRepository) Update(order *models.MaintenanceOrder) error {
	return r.db.Save(order).Error
}

// Delete implementa MaintenanceRepository.Delete
func (r *GormMaintenanceRepository) Delete(id uint) error {
	return r.db.Delete(&models.MaintenanceOrder{}, id).Error
}

// GetAll implementa MaintenanceRepository.GetAll
func (r *GormMaintenanceRepository) GetAll(filters *models.MaintenanceOrderFilters) ([]models.MaintenanceOrder, int, error) {
	var orders []models.MaintenanceOrder
	var total int64

	query := r.db.Model(&models.MaintenanceOrder{})

	if filters != nil {
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		if filters.Priority != nil {
			query = query.Where("priority = ?", *filters.Priority)
		}
		if filters.BranchID != nil {
			query = query.Where("branch_id = ?", *filters.BranchID)
		}
		if filters.EquipmentID != nil {
			query = query.Where("equipment_id = ?", *filters.EquipmentID)
		}
		if filters.ProviderID != nil {
			query = query.Where("assigned_provider_id = ?", *filters.ProviderID)
		}
		if filters.SearchTerm != nil {
			searchTerm := "%" + *filters.SearchTerm + "%"
			query = query.Where("title LIKE ? OR description LIKE ?", searchTerm, searchTerm)
		}
	}

	// Contar total antes de aplicar paginação
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Aplicar paginação
	if filters != nil && filters.Limit > 0 {
		offset := (filters.Page - 1) * filters.Limit
		query = query.Offset(offset).Limit(filters.Limit)
	}

	if err := query.Find(&orders).Error; err != nil {
		return nil, 0, err
	}

	return orders, int(total), nil
}

// GetAllDetailed implementa MaintenanceRepository.GetAllDetailed
func (r *GormMaintenanceRepository) GetAllDetailed() ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrder
	if err := r.db.Preload("Branch").
		Preload("CreatedByUser").
		Preload("AssignedProvider").
		Preload("CostItems").
		Preload("Interactions").
		Find(&orders).Error; err != nil {
		return nil, err
	}

	var detailedOrders []models.MaintenanceOrderDetailed
	for _, order := range orders {
		// Converter para MaintenanceOrderEnt para compatibilidade
		entOrder := order.ConvertToEnt()

		detailed := models.MaintenanceOrderDetailed{
			ID: entOrder.ID,
			// OrderNumber removido pois coluna não existe
			Problem:       entOrder.Problem,
			Description:   entOrder.Problem,
			Status:        string(entOrder.Status),
			Priority:      string(entOrder.Priority),
			Equipment:     fmt.Sprintf("ID: %d", entOrder.EquipmentID),
			CreatedAt:     entOrder.CreatedAt,
			UpdatedAt:     entOrder.UpdatedAt,
			TotalCost:     entOrder.ActualCost,
			StartDate:     &entOrder.OpenDate,
			CompletedDate: entOrder.CompletionDate,
		}
		detailedOrders = append(detailedOrders, detailed)
	}

	return detailedOrders, nil
}

// FindByStatus implementa MaintenanceRepository.FindByStatus
func (r *GormMaintenanceRepository) FindByStatus(status string) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	if err := r.db.Where("status = ?", status).Find(&orders).Error; err != nil {
		return nil, err
	}
	return orders, nil
}

// FindByBranchID implementa MaintenanceRepository.FindByBranchID
func (r *GormMaintenanceRepository) FindByBranchID(branchID uint) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	if err := r.db.Where("branch_id = ?", branchID).Find(&orders).Error; err != nil {
		return nil, err
	}
	return orders, nil
}

// FindByEquipmentID implementa MaintenanceRepository.FindByEquipmentID
func (r *GormMaintenanceRepository) FindByEquipmentID(equipmentID uint) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	if err := r.db.Where("equipment_id = ?", equipmentID).Find(&orders).Error; err != nil {
		return nil, err
	}
	return orders, nil
}

// FindByServiceProviderID implementa MaintenanceRepository.FindByServiceProviderID
func (r *GormMaintenanceRepository) FindByServiceProviderID(providerID uint) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	if err := r.db.Where("assigned_provider_id = ?", providerID).Find(&orders).Error; err != nil {
		return nil, err
	}
	return orders, nil
}

// UpdateStatus implementa MaintenanceRepository.UpdateStatus
func (r *GormMaintenanceRepository) UpdateStatus(id uint, status string, userID uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": now,
	}

	// Adicionar campos específicos baseado no status
	switch status {
	case string(models.StatusCompleted):
		updates["completion_date"] = now
	case string(models.StatusCanceled):
		updates["cancelled_at"] = now
		updates["cancelled_by_user_id"] = userID
	}

	return r.db.Model(&models.MaintenanceOrder{}).Where("id = ?", id).Updates(updates).Error
}

// UpdateApprovalStatus implementa MaintenanceRepository.UpdateApprovalStatus
func (r *GormMaintenanceRepository) UpdateApprovalStatus(id uint, approved bool, approvedBy uint, notes string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"updated_at": now,
	}

	if approved {
		updates["approved_by_user_id"] = approvedBy
		updates["approved_at"] = now
		updates["status"] = models.StatusApproved
	} else {
		updates["rejected_by_user_id"] = approvedBy
		updates["rejection_reason"] = notes
		updates["status"] = models.StatusRejected
	}

	return r.db.Model(&models.MaintenanceOrder{}).Where("id = ?", id).Updates(updates).Error
}

// UpdatePaymentStatus implementa MaintenanceRepository.UpdatePaymentStatus
func (r *GormMaintenanceRepository) UpdatePaymentStatus(id uint, paid bool, paidBy uint, notes string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"updated_at": now,
	}

	if paid {
		updates["paid_at"] = now
		updates["paid_by_user_id"] = paidBy
	} else {
		updates["paid_at"] = nil
		updates["paid_by_user_id"] = nil
	}

	return r.db.Model(&models.MaintenanceOrder{}).Where("id = ?", id).Updates(updates).Error
}

// AddNote implementa MaintenanceRepository.AddNote
func (r *GormMaintenanceRepository) AddNote(orderID uint, userID uint, content string) error {
	interaction := &models.Interaction{
		MaintenanceOrderID: orderID,
		UserID:             userID,
		Message:            content,
		Timestamp:          time.Now(),
	}
	return r.db.Create(interaction).Error
}

// AddMaterial implementa MaintenanceRepository.AddMaterial
func (r *GormMaintenanceRepository) AddMaterial(orderID uint, material *models.Material) error {
	material.MaintenanceOrderID = orderID
	return r.db.Create(material).Error
}

// FindCostsByOrderID implementa MaintenanceRepository.FindCostsByOrderID
func (r *GormMaintenanceRepository) FindCostsByOrderID(orderID uint) ([]models.CostItem, error) {
	var costs []models.CostItem
	if err := r.db.Where("maintenance_order_id = ?", orderID).Find(&costs).Error; err != nil {
		return nil, err
	}
	return costs, nil
}

// CreateCost implementa MaintenanceRepository.CreateCost
func (r *GormMaintenanceRepository) CreateCost(cost *models.CostItem) (*models.CostItem, error) {
	if err := r.db.Create(cost).Error; err != nil {
		return nil, err
	}
	return cost, nil
}

// CreateInteraction implementa MaintenanceRepository.CreateInteraction
func (r *GormMaintenanceRepository) CreateInteraction(interaction *models.Interaction) (*models.Interaction, error) {
	if err := r.db.Create(interaction).Error; err != nil {
		return nil, err
	}
	return interaction, nil
}

// GetMetrics implementa MaintenanceRepository.GetMetrics
func (r *GormMaintenanceRepository) GetMetrics() (*models.MaintenanceMetrics, error) {
	metrics := &models.MaintenanceMetrics{
		OrdersByStatus:   make(map[string]int),
		OrdersByPriority: make(map[string]int),
		LastUpdate:       time.Now(),
	}

	// Total de ordens
	var total int64
	if err := r.db.Model(&models.MaintenanceOrder{}).Count(&total).Error; err != nil {
		return nil, err
	}
	metrics.TotalOrders = int(total)

	// Contagem por status
	var statusCounts []struct {
		Status string
		Count  int64
	}
	if err := r.db.Model(&models.MaintenanceOrder{}).
		Select("status, count(*) as count").
		Group("status").
		Find(&statusCounts).Error; err != nil {
		return nil, err
	}

	for _, sc := range statusCounts {
		metrics.OrdersByStatus[sc.Status] = int(sc.Count)
	}

	return metrics, nil
}

// GetExtendedMetrics implementa MaintenanceRepository.GetExtendedMetrics
func (r *GormMaintenanceRepository) GetExtendedMetrics() (*models.MaintenanceMetrics, error) {
	metrics, err := r.GetMetrics()
	if err != nil {
		return nil, err
	}

	// Adicionar métricas estendidas
	var priorityCounts []struct {
		Priority string
		Count    int64
	}
	if err := r.db.Model(&models.MaintenanceOrder{}).
		Select("priority, count(*) as count").
		Group("priority").
		Find(&priorityCounts).Error; err != nil {
		return nil, err
	}

	for _, pc := range priorityCounts {
		metrics.OrdersByPriority[pc.Priority] = int(pc.Count)
	}

	// Calcular tempo médio de conclusão
	var avgCompletionTime float64
	if err := r.db.Model(&models.MaintenanceOrder{}).
		Where("completion_date IS NOT NULL").
		Select("AVG(TIMESTAMPDIFF(HOUR, created_at, completion_date))").
		Row().
		Scan(&avgCompletionTime); err != nil {
		return nil, err
	}
	metrics.AverageCompletionTime = avgCompletionTime

	// Calcular custo total e médio
	if err := r.db.Model(&models.MaintenanceOrder{}).
		Select("COALESCE(SUM(total_cost), 0)").
		Row().
		Scan(&metrics.TotalCost); err != nil {
		return nil, err
	}

	if metrics.TotalOrders > 0 {
		metrics.AverageCostPerOrder = metrics.TotalCost / float64(metrics.TotalOrders)
	}

	// Definir período do relatório
	metrics.ReportPeriodEnd = time.Now()
	metrics.ReportPeriodStart = metrics.ReportPeriodEnd.AddDate(0, -1, 0) // último mês

	return metrics, nil
}

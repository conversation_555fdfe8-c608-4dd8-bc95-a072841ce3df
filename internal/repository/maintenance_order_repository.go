package repository

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"tradicao/internal/models"

	"gorm.io/gorm"
)

// Constantes para nomes de tabelas
const (
	TableMaintenanceOrders = "maintenance_orders"
	TableBranches          = "branches"
	TableEquipment         = "equipment"
	TableUsers             = "users"
	TableServiceProviders  = "service_providers"
)

// Funções auxiliares
// addOrderJoins adiciona os joins necessários para consultar ordens de manutenção
func addOrderJoins(query *gorm.DB, tables ...string) *gorm.DB {
	for _, table := range tables {
		switch table {
		case TableMaintenanceOrders:
			query = query.Table(TableMaintenanceOrders + " as mo")
			query = query.Select("mo.*, b.name as branch_name, e.name as equipment_name, " +
				"creator.name as created_by_name, assignee.name as assigned_to_name, " +
				"p.name as provider_name")
			query = query.Joins("LEFT JOIN " + TableBranches + " as b ON mo.branch_id = b.id")
			query = query.Joins("LEFT JOIN " + TableEquipment + " as e ON mo.equipment_id = e.id")
			// query = query.Joins("LEFT JOIN " + TableUsers + " as creator ON mo.created_by_user_id = creator.id") // REMOVIDO: coluna created_by_user_id não existe
			query = query.Joins("LEFT JOIN " + TableUsers + " as assignee ON mo.assigned_to_user_id = assignee.id")
			query = query.Joins("LEFT JOIN " + TableServiceProviders + " as p ON mo.service_provider_id = p.id")
		}
	}
	return query
}

// MaintenanceOrderRepository é o repositório para operações com ordens de manutenção
type MaintenanceOrderRepository struct {
	db *gorm.DB
}

// NewMaintenanceOrderRepository cria uma nova instância do repositório de ordens de manutenção
func NewMaintenanceOrderRepository(db *gorm.DB) *MaintenanceOrderRepository {
	return &MaintenanceOrderRepository{db: db}
}

// GetAll retorna todas as ordens de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - filters: Filtros para a busca
//   - userID: ID do usuário
//   - userRole: Papel do usuário
//   - page: Número da página
//   - limit: Limite de registros por página
func (r *MaintenanceOrderRepository) GetAll(ctx context.Context, filters map[string]interface{}, userID int64, userRole string, page, limit int) ([]models.MaintenanceOrderDetailed, int, error) {
	var orders []models.MaintenanceOrderDetailed
	var total int64

	query := r.db.WithContext(ctx)

	// Excluir ordens de teste (com is_test = true ou flagged como test)
	// Filtro mais permissivo para não excluir ordens válidas
	// query = query.Where("(is_test IS NULL OR is_test = false)") // REMOVIDO: coluna is_test não existe

	// Adicionar filtros para excluir a ordem #18 que está causando problemas
	query = query.Where("id != 18")

	// Aplicar filtros
	if filters != nil {
		for key, value := range filters {
			// Tratamento especial para filtros específicos
			switch key {
			case "technician_id":
				// Filtrar por técnico
				query = query.Where("technician_id = ?", value)
				// Log para debug
				log.Printf("[DEBUG] Aplicando filtro por técnico ID: %v", value)
			case "date":
				// Filtrar por data específica
				query = query.Where("DATE(created_at) = ? OR DATE(scheduled_date) = ? OR DATE(due_date) = ?", value, value, value)
			case "month", "year":
				// Estes serão tratados depois
				continue
			case "excluir_ids":
				// Tratar IDs para exclusão
				if ids, ok := value.(string); ok && ids != "" {
					idList := strings.Split(ids, ",")
					query = query.Where("id NOT IN (?)", idList)
				}
				continue
			default:
				// Filtro padrão
				query = query.Where(key+" = ?", value)
			}
		}

		// Filtrar por mês e ano se ambos estiverem presentes
		if month, hasMonth := filters["month"]; hasMonth {
			if year, hasYear := filters["year"]; hasYear {
				// Construir condição para filtrar por mês e ano
				query = query.Where(
					"(EXTRACT(MONTH FROM created_at) = ? AND EXTRACT(YEAR FROM created_at) = ?) OR "+
						"(EXTRACT(MONTH FROM scheduled_date) = ? AND EXTRACT(YEAR FROM scheduled_date) = ?) OR "+
						"(EXTRACT(MONTH FROM due_date) = ? AND EXTRACT(YEAR FROM due_date) = ?)",
					month, year, month, year, month, year)
			}
		}
	}

	// IMPORTANTE: Aplicar filtros por usuário com alta prioridade
	// Apenas admin, gerente e financeiro podem ver todas as ordens
	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" || userRole == "branch_user" {
			// CORREÇÃO: Para usuários de filial, SEMPRE mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				query = query.Where("branch_id = ?", *user.BranchID)
				log.Printf("[SECURITY] Filtrando ordens para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					query = query.Where("branch_id IN ?", branchIDs)
					log.Printf("[SECURITY] Filtrando ordens para usuário de filial (ID: %d) com múltiplas filiais: %v", userID, branchIDs)
				} else {
					// CORREÇÃO: Se não conseguir determinar a filial, negar acesso a todas as ordens (princípio de menor privilégio)
					log.Printf("[SECURITY] Não foi possível determinar a filial do usuário (ID: %d). Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				query = query.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[SECURITY] Filtrando ordens para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					query = query.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[SECURITY] Filtrando ordens para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					// CORREÇÃO: Se não conseguir determinar o prestador, negar acesso a todas as ordens
					log.Printf("[SECURITY] Não foi possível determinar o prestador do usuário (ID: %d). Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[SECURITY] Filtrando ordens para técnico (ID: %d)", userID)
		}
	} else {
		// Administradores, gerentes e financeiro podem ver todas as ordens, mas ainda excluindo as de teste
		log.Printf("[INFO] Usuário com perfil %s tem acesso a todas as ordens (exceto teste)", userRole)
	}

	// Contar total
	if err := query.Model(&models.MaintenanceOrder{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Aplicar paginação
	offset := (page - 1) * limit
	if limit > 0 {
		query = query.Offset(offset).Limit(limit)
	}

	// Log da query final para debug
	log.Printf("[DEBUG-QUERY] Executando consulta de ordens para usuário ID: %d, Role: %s", userID, userRole)

	// Executar a consulta
	err := query.Order("created_at DESC").Find(&orders).Error

	// Log dos resultados
	log.Printf("[DEBUG-RESULTS] Encontradas %d ordens de %d total para usuário ID: %d", len(orders), int(total), userID)

	return orders, int(total), err
}

// GetByID retorna uma ordem de manutenção pelo ID
func (r *MaintenanceOrderRepository) GetByID(ctx context.Context, id uint) (*models.MaintenanceOrder, error) {
	// Verificar se a ordem existe

	var order models.MaintenanceOrder
	err := r.db.WithContext(ctx).First(&order, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &order, nil
}

// GetOrderByID retorna uma ordem detalhada pelo ID
func (r *MaintenanceOrderRepository) GetOrderByID(ctx context.Context, orderID int64) (*models.MaintenanceOrderDetailed, error) {
	// Verificar se a ordem existe

	var order models.MaintenanceOrderDetailed
	err := r.db.WithContext(ctx).Table("maintenance_orders mo").
		Select(`
			mo.*,
			b.name as branch_name,
			e.type as equipment_type,
			e.brand as equipment_brand,
			e.model as equipment_model,
			sp.name as service_provider_name
		`).
		Joins("LEFT JOIN branches b ON mo.branch_id = b.id").
		Joins("LEFT JOIN equipment e ON mo.equipment_id = e.id").
		Joins("LEFT JOIN service_providers sp ON mo.service_provider_id = sp.id").
		Where("mo.id = ?", orderID).
		First(&order).Error
	if err != nil {
		return nil, err
	}

	// Carregar notas separadamente
	var notes []models.Note
	if err := r.db.WithContext(ctx).
		Where("maintenance_order_id = ?", orderID).
		Order("created_at DESC").
		Find(&notes).Error; err == nil {
		order.Notes = notes
	}

	// Carregar materiais separadamente
	var materials []models.Material
	if err := r.db.WithContext(ctx).
		Where("maintenance_order_id = ?", orderID).
		Find(&materials).Error; err == nil {
		order.Materials = materials
	}

	// Carregar fotos separadamente
	var photos []models.OrderPhoto
	if err := r.db.WithContext(ctx).
		Where("order_id = ?", orderID).
		Find(&photos).Error; err == nil {
		order.Photos = photos
	}

	return &order, nil
}

// Create cria uma nova ordem de manutenção
func (r *MaintenanceOrderRepository) Create(ctx context.Context, order *models.MaintenanceOrder) error {
	return r.db.WithContext(ctx).Create(order).Error
}

// Update atualiza uma ordem de manutenção existente
func (r *MaintenanceOrderRepository) Update(ctx context.Context, order *models.MaintenanceOrder) error {
	// Verificar se a ordem existe

	return r.db.WithContext(ctx).Save(order).Error
}

// Delete remove uma ordem de manutenção
func (r *MaintenanceOrderRepository) Delete(ctx context.Context, id uint) error {
	// Verificar se a ordem existe

	result := r.db.WithContext(ctx).Delete(&models.MaintenanceOrder{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// CreateInteraction cria uma nova interação
func (r *MaintenanceOrderRepository) CreateInteraction(ctx context.Context, interaction *models.Interaction) (*models.Interaction, error) {
	err := r.db.WithContext(ctx).Create(interaction).Error
	if err != nil {
		return nil, err
	}
	return interaction, nil
}

// AddMaterial adiciona um material a uma ordem
func (r *MaintenanceOrderRepository) AddMaterial(ctx context.Context, orderID int64, material models.MaterialRequest, userID int64) error {
	// Implementar lógica de adição de material
	return nil
}

// AddNote adiciona uma nota a uma ordem
func (r *MaintenanceOrderRepository) AddNote(ctx context.Context, orderID int64, content string, userID int64) error {
	// Implementar lógica de adição de nota
	return nil
}

// GetMetrics retorna métricas das ordens
func (r *MaintenanceOrderRepository) GetMetrics(ctx context.Context, filters map[string]interface{}, userID int64, userRole string) (*models.MaintenanceOrderMetricsV2, error) {
	metrics := &models.MaintenanceOrderMetricsV2{
		LastUpdate:        time.Now(),
		ReportPeriodStart: time.Now().AddDate(0, -1, 0), // último mês
		ReportPeriodEnd:   time.Now(),
		OrdersByMonth:     make(map[string]int64),
		OrdersByStation:   make(map[string]int64),
		OrdersByStatus:    make(map[string]int64),
	}

	db := r.db.WithContext(ctx)

	// Aplicar filtros
	if filters != nil {
		for key, value := range filters {
			db = db.Where(key+" = ?", value)
		}
	}

	// Aplicar filtros por usuário (apenas se não for admin, gerente ou financeiro)
	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// Para usuários de filial, mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				db = db.Where("branch_id = ?", *user.BranchID)
				log.Printf("[FILTER] Filtrando métricas para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					db = db.Where("branch_id IN ?", branchIDs)
					log.Printf("[FILTER] Filtrando métricas para usuário de filial (ID: %d) com múltiplas filiais: %v", userID, branchIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar a filial do usuário (ID: %d) para métricas. Negando acesso a todas as métricas.", userID)
					db = db.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				db = db.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando métricas para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					db = db.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando métricas para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para métricas.", userID)
					db = db.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			db = db.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando métricas para técnico (ID: %d)", userID)
		}
	}

	// Contagem total
	if err := db.Model(&models.MaintenanceOrder{}).Count(&metrics.TotalOrders).Error; err != nil {
		return nil, err
	}

	// Contagens por status
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := db.Model(&models.MaintenanceOrder{}).
		Select("status, count(*) as count").
		Group("status").
		Find(&statusCounts).Error; err != nil {
		return nil, err
	}

	for _, sc := range statusCounts {
		metrics.OrdersByStatus[sc.Status] = sc.Count
	}

	// Tempo médio de conclusão
	var avgCompletionTime float64
	if err := db.Model(&models.MaintenanceOrder{}).
		Where("status = ? AND completion_date IS NOT NULL", string(models.StatusCompleted)).
		Select("AVG(EXTRACT(EPOCH FROM (completion_date - created_at))/3600) as avg").
		Scan(&avgCompletionTime).Error; err != nil {
		return nil, err
	}
	metrics.AverageCompletionTime = avgCompletionTime

	return metrics, nil
}

// GetMetricsByStatus retorna métricas por status
func (r *MaintenanceOrderRepository) GetMetricsByStatus(ctx context.Context, userID int64, userRole string) (map[models.OrderStatus]int, error) {
	metrics := make(map[models.OrderStatus]int)
	db := r.db.WithContext(ctx)

	// Aplicar filtros por usuário (apenas se não for admin, gerente ou financeiro)
	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// Para usuários de filial, mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				db = db.Where("branch_id = ?", *user.BranchID)
				log.Printf("[FILTER] Filtrando por filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					db = db.Where("branch_id IN ?", branchIDs)
					log.Printf("[FILTER] Filtrando por múltiplas filiais: %v", branchIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar a filial do usuário (ID: %d). Negando acesso a todas as métricas por status.", userID)
					db = db.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				db = db.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando por prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					db = db.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando por múltiplos prestadores: %v", providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para métricas por status.", userID)
					db = db.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			db = db.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando por técnico (ID: %d)", userID)
		}
	}

	// Contagens por status
	statusCounts := []struct {
		Status string
		Count  int
	}{}

	if err := db.Model(&models.MaintenanceOrder{}).
		Select("status, count(*) as count").
		Group("status").
		Find(&statusCounts).Error; err != nil {
		return nil, err
	}

	for _, sc := range statusCounts {
		metrics[models.OrderStatus(sc.Status)] = sc.Count
	}

	return metrics, nil
}

// GetOrdersByPriority retorna métricas por prioridade
func (r *MaintenanceOrderRepository) GetOrdersByPriority(ctx context.Context, userID int64, userRole string) (map[models.PriorityLevel]int, error) {
	metrics := make(map[models.PriorityLevel]int)

	query := r.db.WithContext(ctx).Model(&models.MaintenanceOrder{})
	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		query = query.Where("branch_id IN (SELECT branch_id FROM user_branches WHERE user_id = ?)", userID)
	}

	var priorityCounts []struct {
		Priority models.PriorityLevel
		Count    int
	}

	err := query.Select("priority, count(*) as count").
		Group("priority").
		Find(&priorityCounts).Error

	if err != nil {
		return nil, err
	}

	for _, pc := range priorityCounts {
		metrics[pc.Priority] = pc.Count
	}

	return metrics, nil
}

// GetStations retorna as estações disponíveis para o usuário
func (r *MaintenanceOrderRepository) GetStations(ctx context.Context, userID int64, userRole string) ([]models.StationSummary, error) {
	var stations []models.StationSummary
	db := r.db.WithContext(ctx)

	// Aplicar filtros por usuário (apenas se não for admin, gerente ou financeiro)
	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		db = db.Where("id IN (SELECT branch_id FROM user_branches WHERE user_id = ?)", userID)
	}

	err := db.Find(&stations).Error
	return stations, err
}

// GetRecentOrders retorna as ordens mais recentes
func (r *MaintenanceOrderRepository) GetRecentOrders(ctx context.Context, userID int64, userRole string, limit int) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	db := r.db.WithContext(ctx)

	// Aplicar filtros por usuário (apenas se não for admin, gerente ou financeiro)
	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// Para usuários de filial, mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				db = db.Where("branch_id = ?", *user.BranchID)
				log.Printf("[FILTER] Filtrando ordens recentes para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					db = db.Where("branch_id IN ?", branchIDs)
					log.Printf("[FILTER] Filtrando ordens recentes para usuário de filial (ID: %d) com múltiplas filiais: %v", userID, branchIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar a filial do usuário (ID: %d) para ordens recentes. Negando acesso a todas as ordens.", userID)
					db = db.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				db = db.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando ordens recentes para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					db = db.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando ordens recentes para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para ordens recentes.", userID)
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			db = db.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens recentes para técnico (ID: %d)", userID)
		}
	}

	err := db.Order("created_at DESC").Limit(limit).Find(&orders).Error
	if err != nil {
		return nil, err
	}

	// Para cada ordem, carregar dados relacionados
	for i := range orders {
		orderID := orders[i].ID

		// Carregar notas
		var notes []models.Note
		if err := r.db.WithContext(ctx).
			Where("maintenance_order_id = ?", orderID).
			Order("created_at DESC").
			Find(&notes).Error; err == nil {
			orders[i].Notes = notes
		}
	}

	return orders, nil
}

// GetByStatus retorna ordens de manutenção por status
func (r *MaintenanceOrderRepository) GetByStatus(ctx context.Context, status string, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	query := r.db.WithContext(ctx).Where("status = ?", status)

	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// Para usuários de filial, mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				query = query.Where("branch_id = ?", *user.BranchID)
				log.Printf("[FILTER] Filtrando ordens por status para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					query = query.Where("branch_id IN ?", branchIDs)
					log.Printf("[FILTER] Filtrando ordens por status para usuário de filial (ID: %d) com múltiplas filiais: %v", userID, branchIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar a filial do usuário (ID: %d) para ordens por status. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				query = query.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando ordens por status para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					query = query.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando ordens por status para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para ordens por status.", userID)
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens por status para técnico (ID: %d)", userID)
		}
	}

	if err := query.Find(&orders).Error; err != nil {
		log.Printf("Erro ao buscar ordens por status: %v", err)
		return nil, err
	}

	return orders, nil
}

// GetByBranch retorna ordens de manutenção por filial
func (r *MaintenanceOrderRepository) GetByBranch(ctx context.Context, branchID uint, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	query := r.db.WithContext(ctx)

	// Aplicar o filtro por filial solicitado
	query = query.Where("branch_id = ?", branchID)

	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// CORREÇÃO CRÍTICA: Para usuários de filial, verificar se a filial solicitada corresponde à sua própria filial
			var userBranchIDs []uint

			// Primeiro tentar obter a filial principal do usuário
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				userBranchIDs = append(userBranchIDs, *user.BranchID)
			}

			// Também verificar filiais adicionais na tabela user_branches
			var additionalBranchIDs []uint
			if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &additionalBranchIDs).Error; err == nil {
				userBranchIDs = append(userBranchIDs, additionalBranchIDs...)
			}

			// Verificar se a filial solicitada pertence ao usuário
			branchAuthorized := false
			for _, userBranchID := range userBranchIDs {
				if userBranchID == branchID {
					branchAuthorized = true
					break
				}
			}

			if !branchAuthorized {
				// BLOQUEIO DE SEGURANÇA: Usuário tentando acessar filial que não lhe pertence
				log.Printf("[SECURITY] ALERTA: Usuário de filial (ID: %d) tentando acessar ordens da filial %d não autorizada", userID, branchID)
				// Condição sempre falsa para não retornar resultados - barreira de segurança
				query = query.Where("1 = 0")
			} else {
				log.Printf("[SECURITY] Usuário de filial (ID: %d) autorizado a acessar ordens da filial %d", userID, branchID)
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				query = query.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando ordens por filial para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					query = query.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando ordens por filial para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para ordens por filial.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens por filial para técnico (ID: %d)", userID)
		}
	}

	// Excluir ordens problemáticas (#18)
	query = query.Where("id != 18")

	if err := query.Find(&orders).Error; err != nil {
		log.Printf("Erro ao buscar ordens por filial: %v", err)
		return nil, err
	}

	return orders, nil
}

// GetByEquipment retorna ordens de manutenção por equipamento
func (r *MaintenanceOrderRepository) GetByEquipment(ctx context.Context, equipmentID uint, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	query := r.db.WithContext(ctx).Where("equipment_id = ?", equipmentID)

	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// CORREÇÃO CRÍTICA: Para usuários de filial, verificar suas filiais autorizadas
			var userBranchIDs []uint

			// Primeiro tentar obter a filial principal do usuário
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				userBranchIDs = append(userBranchIDs, *user.BranchID)
			}

			// Também verificar filiais adicionais na tabela user_branches
			var additionalBranchIDs []uint
			if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &additionalBranchIDs).Error; err == nil {
				userBranchIDs = append(userBranchIDs, additionalBranchIDs...)
			}

			if len(userBranchIDs) > 0 {
				// Filtrar apenas ordens de equipamentos das filiais autorizadas
				query = query.Where("branch_id IN ?", userBranchIDs)
				log.Printf("[SECURITY] Filtrando ordens de equipamento para usuário de filial (ID: %d) com filiais autorizadas: %v", userID, userBranchIDs)
			} else {
				// Se não conseguir determinar filiais autorizadas, bloquear acesso
				log.Printf("[SECURITY] ALERTA: Não foi possível determinar filiais autorizadas do usuário (ID: %d). Negando acesso a todas as ordens.", userID)
				query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				query = query.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando ordens por equipamento para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					query = query.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando ordens por equipamento para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para ordens por equipamento. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens por equipamento para técnico (ID: %d)", userID)
		}
	}

	// Excluir ordens problemáticas (#18)
	query = query.Where("id != 18")

	if err := query.Find(&orders).Error; err != nil {
		log.Printf("Erro ao buscar ordens por equipamento: %v", err)
		return nil, err
	}

	return orders, nil
}

// GetByProvider retorna ordens de manutenção por prestador
func (r *MaintenanceOrderRepository) GetByProvider(ctx context.Context, providerID uint, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	query := r.db.WithContext(ctx).Where("service_provider_id = ?", providerID)

	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// CORREÇÃO CRÍTICA: Para usuários de filial, verificar suas filiais autorizadas
			var userBranchIDs []uint

			// Primeiro tentar obter a filial principal do usuário
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				userBranchIDs = append(userBranchIDs, *user.BranchID)
			}

			// Também verificar filiais adicionais na tabela user_branches
			var additionalBranchIDs []uint
			if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &additionalBranchIDs).Error; err == nil {
				userBranchIDs = append(userBranchIDs, additionalBranchIDs...)
			}

			if len(userBranchIDs) > 0 {
				// Filtrar apenas ordens do prestador solicitado que pertencem às filiais autorizadas
				query = query.Where("branch_id IN ?", userBranchIDs)
				log.Printf("[SECURITY] Filtrando ordens de prestador para usuário de filial (ID: %d) com filiais autorizadas: %v", userID, userBranchIDs)
			} else {
				// Se não conseguir determinar filiais autorizadas, bloquear acesso
				log.Printf("[SECURITY] ALERTA: Não foi possível determinar filiais autorizadas do usuário (ID: %d). Negando acesso a todas as ordens.", userID)
				query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// CORREÇÃO CRÍTICA: Para prestadores, verificar se o prestador solicitado corresponde ao seu próprio prestador
			var userProviderIDs []uint

			// Primeiro tentar obter o prestador principal do usuário
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				userProviderIDs = append(userProviderIDs, *user.ServiceProviderID)
			}

			// Também verificar prestadores adicionais na tabela provider_users
			var additionalProviderIDs []uint
			if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &additionalProviderIDs).Error; err == nil {
				userProviderIDs = append(userProviderIDs, additionalProviderIDs...)
			}

			// Verificar se o prestador solicitado pertence ao usuário
			providerAuthorized := false
			for _, userProviderID := range userProviderIDs {
				if userProviderID == providerID {
					providerAuthorized = true
					break
				}
			}

			if !providerAuthorized {
				// BLOQUEIO DE SEGURANÇA: Usuário tentando acessar prestador que não lhe pertence
				log.Printf("[SECURITY] ALERTA: Usuário prestador (ID: %d) tentando acessar ordens do prestador %d não autorizado", userID, providerID)
				// Condição sempre falsa para não retornar resultados - barreira de segurança
				query = query.Where("1 = 0")
			} else {
				log.Printf("[SECURITY] Usuário prestador (ID: %d) autorizado a acessar ordens do prestador %d", userID, providerID)
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens por prestador para técnico (ID: %d)", userID)
		}
	}

	// Excluir ordens problemáticas (#18)
	query = query.Where("id != 18")

	if err := query.Find(&orders).Error; err != nil {
		log.Printf("Erro ao buscar ordens por prestador: %v", err)
		return nil, err
	}

	return orders, nil
}

// GetOverdue retorna ordens de manutenção atrasadas
func (r *MaintenanceOrderRepository) GetOverdue(ctx context.Context, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	query := r.db.WithContext(ctx).Where("due_date < ? AND status NOT IN (?)", time.Now(), []string{string(models.StatusCompleted), string(models.StatusCanceled)})

	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// Para usuários de filial, mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				query = query.Where("branch_id = ?", *user.BranchID)
				log.Printf("[FILTER] Filtrando ordens atrasadas para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					query = query.Where("branch_id IN ?", branchIDs)
					log.Printf("[FILTER] Filtrando ordens atrasadas para usuário de filial (ID: %d) com múltiplas filiais: %v", userID, branchIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar a filial do usuário (ID: %d) para ordens atrasadas. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				query = query.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando ordens atrasadas para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					query = query.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando ordens atrasadas para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para ordens atrasadas. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens atrasadas para técnico (ID: %d)", userID)
		}
	}

	if err := query.Find(&orders).Error; err != nil {
		log.Printf("Erro ao buscar ordens atrasadas: %v", err)
		return nil, err
	}

	return orders, nil
}

// GetUpcoming retorna ordens de manutenção próximas do vencimento
func (r *MaintenanceOrderRepository) GetUpcoming(ctx context.Context, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	query := r.db.WithContext(ctx).Where("due_date BETWEEN ? AND ? AND status NOT IN (?)",
		time.Now(),
		time.Now().AddDate(0, 0, 7),
		[]string{string(models.StatusCompleted), string(models.StatusCanceled)})

	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// Para usuários de filial, mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				query = query.Where("branch_id = ?", *user.BranchID)
				log.Printf("[FILTER] Filtrando ordens próximas para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					query = query.Where("branch_id IN ?", branchIDs)
					log.Printf("[FILTER] Filtrando ordens próximas para usuário de filial (ID: %d) com múltiplas filiais: %v", userID, branchIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar a filial do usuário (ID: %d) para ordens próximas. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				query = query.Where("service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando ordens próximas para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					query = query.Where("service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando ordens próximas para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para ordens próximas. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens próximas para técnico (ID: %d)", userID)
		}
	}

	if err := query.Find(&orders).Error; err != nil {
		log.Printf("Erro ao buscar ordens próximas: %v", err)
		return nil, err
	}

	return orders, nil
}

// GetCosts retorna os custos de uma ordem de manutenção
func (r *MaintenanceOrderRepository) GetCosts(ctx context.Context, orderID int64) (*models.MaintenanceOrderCosts, error) {
	var costs models.MaintenanceOrderCosts
	err := r.db.WithContext(ctx).Table("maintenance_orders").
		Select(`
			COALESCE(SUM(material_cost), 0) as material_cost,
			COALESCE(SUM(labor_cost), 0) as labor_cost,
			COALESCE(SUM(material_cost + labor_cost), 0) as total_cost
		`).
		Where("id = ?", orderID).
		Scan(&costs).Error
	if err != nil {
		return nil, err
	}
	return &costs, nil
}

// GetInteractions retorna as interações de uma ordem de manutenção
func (r *MaintenanceOrderRepository) GetInteractions(ctx context.Context, orderID int64) ([]models.Interaction, error) {
	var interactions []models.Interaction
	err := r.db.WithContext(ctx).
		Where("maintenance_order_id = ?", orderID).
		Order("created_at DESC").
		Find(&interactions).Error
	if err != nil {
		return nil, err
	}
	return interactions, nil
}

// GetMaterials retorna os materiais de uma ordem de manutenção
func (r *MaintenanceOrderRepository) GetMaterials(ctx context.Context, orderID int64) ([]models.Material, error) {
	var materials []models.Material
	err := r.db.WithContext(ctx).
		Where("maintenance_order_id = ?", orderID).
		Find(&materials).Error
	if err != nil {
		return nil, err
	}
	return materials, nil
}

// GetNotes retorna as notas de uma ordem de manutenção
func (r *MaintenanceOrderRepository) GetNotes(ctx context.Context, orderID int64) ([]models.Note, error) {
	var notes []models.Note
	err := r.db.WithContext(ctx).
		Where("maintenance_order_id = ?", orderID).
		Order("created_at DESC").
		Find(&notes).Error
	if err != nil {
		return nil, err
	}
	return notes, nil
}

// GetOrdersByDateRange retorna ordens de manutenção em um intervalo de datas
func (r *MaintenanceOrderRepository) GetOrdersByDateRange(ctx context.Context, startDate, endDate time.Time, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	var orders []models.MaintenanceOrderDetailed
	query := r.db.WithContext(ctx).Table("maintenance_orders mo").
		Select(`
			mo.*,
			b.name as branch_name,
			e.type as equipment_type,
			e.brand as equipment_brand,
			e.model as equipment_model,
			sp.name as service_provider_name
		`).
		Joins("LEFT JOIN branches b ON mo.branch_id = b.id").
		Joins("LEFT JOIN equipment e ON mo.equipment_id = e.id").
		Joins("LEFT JOIN service_providers sp ON mo.service_provider_id = sp.id").
		Where("mo.created_at BETWEEN ? AND ?", startDate, endDate)

	if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
		if userRole == "filial" {
			// Para usuários de filial, mostrar apenas ordens da sua filial
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.BranchID != nil {
				query = query.Where("mo.branch_id = ?", *user.BranchID)
				log.Printf("[FILTER] Filtrando ordens por data para usuário de filial (ID: %d, BranchID: %d)", userID, *user.BranchID)
			} else {
				// Se não conseguir obter o BranchID do usuário, tentar obter da tabela user_branches
				var branchIDs []uint
				if err := r.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
					query = query.Where("mo.branch_id IN ?", branchIDs)
					log.Printf("[FILTER] Filtrando ordens por data para usuário de filial (ID: %d) com múltiplas filiais: %v", userID, branchIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar a filial do usuário (ID: %d) para ordens por data. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "prestadores" || userRole == "prestador" {
			// Para prestadores, mostrar ordens atribuídas a eles
			var user models.User
			if err := r.db.First(&user, userID).Error; err == nil && user.ServiceProviderID != nil {
				query = query.Where("mo.service_provider_id = ?", *user.ServiceProviderID)
				log.Printf("[FILTER] Filtrando ordens por data para prestador (ID: %d, ProviderID: %d)", userID, *user.ServiceProviderID)
			} else {
				// Se não conseguir obter o ServiceProviderID do usuário, tentar obter da tabela provider_users
				var providerIDs []uint
				if err := r.db.Table("provider_users").Where("user_id = ?", userID).Pluck("provider_id", &providerIDs).Error; err == nil && len(providerIDs) > 0 {
					query = query.Where("mo.service_provider_id IN ?", providerIDs)
					log.Printf("[FILTER] Filtrando ordens por data para prestador (ID: %d) com múltiplos prestadores: %v", userID, providerIDs)
				} else {
					log.Printf("[FILTER] ATENÇÃO: Não foi possível determinar o prestador do usuário (ID: %d) para ordens por data. Negando acesso a todas as ordens.", userID)
					query = query.Where("1 = 0") // Condição sempre falsa para não retornar nenhum resultado
				}
			}
		} else if userRole == "tecnico" || userRole == "technician" {
			// Para técnicos, mostrar ordens atribuídas a eles através da tabela technician_orders
			query = query.Where("mo.id IN (SELECT order_id FROM technician_orders WHERE technician_id = ?)", userID)
			log.Printf("[FILTER] Filtrando ordens por data para técnico (ID: %d)", userID)
		}
	}

	err := query.Order("mo.created_at DESC").Find(&orders).Error
	if err != nil {
		return nil, err
	}
	return orders, nil
}

// UpdateStatus atualiza o status de uma ordem de manutenção
func (r *MaintenanceOrderRepository) UpdateStatus(ctx context.Context, id uint, status models.OrderStatus, userID uint, notes string) error {
	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		return errors.New("esta ordem não está disponível para atualização. Por favor, contate o suporte")
	}

	return r.db.WithContext(ctx).Model(&models.MaintenanceOrder{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"notes":      notes,
			"updated_at": time.Now(),
		}).Error
}

// UpdatePriority atualiza a prioridade de uma ordem de manutenção
func (r *MaintenanceOrderRepository) UpdatePriority(ctx context.Context, id uint, priority models.PriorityLevel, userID uint, notes string) error {
	// Verificar se a ordem existe

	return r.db.WithContext(ctx).Model(&models.MaintenanceOrder{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"priority":   priority,
			"notes":      notes,
			"updated_at": time.Now(),
		}).Error
}

// FindByTechnician retorna ordens de serviço atribuídas a um técnico
func (r *MaintenanceOrderRepository) FindByTechnician(technicianID uint) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	err := r.db.Where("technician_id = ?", technicianID).Find(&orders).Error
	return orders, err
}

// RemoveTestOrders remove permanentemente todas as ordens de teste/simulação do banco de dados
// Esta função precisa ser executada com cuidado, pois remove definitivamente os registros
func (r *MaintenanceOrderRepository) RemoveTestOrders() error {
	log.Println("[SECURITY] Iniciando processo de remoção de ordens de teste do banco de dados")

	// 1. Identificar todas as ordens de teste explicitamente marcadas
	var testOrderIDs []uint
	if err := r.db.Model(&models.MaintenanceOrder{}).
		Where("is_test = true OR test_flag = true").
		Pluck("id", &testOrderIDs).Error; err != nil {
		log.Printf("[ERROR] Falha ao identificar ordens marcadas como teste: %v", err)
		return err
	}

	// 2. Identificar ordens com padrões de descrição de teste
	var testTextOrderIDs []uint
	testPatterns := []string{
		"%teste%", "%simulação%", "%simulacao%", "%exemplo%",
		"%test%", "%demo%", "%dummy%", "%sample%", "%treinamento%",
		"%qualidade%", "%quality%", "%sandbox%", "%homologação%",
		"%homologacao%", "%validação%", "%validacao%",
	}

	query := r.db.Model(&models.MaintenanceOrder{}).Where("id NOT IN (?)", testOrderIDs)

	// Construir condição OR para cada padrão de teste
	conditions := []string{}
	values := []interface{}{}

	for _, pattern := range testPatterns {
		conditions = append(conditions, "LOWER(description) LIKE LOWER(?)")
		values = append(values, pattern)

		conditions = append(conditions, "LOWER(title) LIKE LOWER(?)")
		values = append(values, pattern)

		conditions = append(conditions, "LOWER(notes) LIKE LOWER(?)")
		values = append(values, pattern)
	}

	if len(conditions) > 0 {
		whereClause := strings.Join(conditions, " OR ")
		if err := query.Where(whereClause, values...).Pluck("id", &testTextOrderIDs).Error; err != nil {
			log.Printf("[ERROR] Falha ao identificar ordens com padrões de teste: %v", err)
			return err
		}
	}

	// 3. Adicionar ordem #18 à lista (causa específica do problema)
	allTestIDs := append(testOrderIDs, testTextOrderIDs...)
	allTestIDs = append(allTestIDs, 18)

	// Remover duplicatas
	uniqueIDs := make(map[uint]bool)
	var finalIDs []uint
	for _, id := range allTestIDs {
		if !uniqueIDs[id] {
			uniqueIDs[id] = true
			finalIDs = append(finalIDs, id)
		}
	}

	// Se não houver ordens para remover, retorne
	if len(finalIDs) == 0 {
		log.Println("[SECURITY] Nenhuma ordem de teste encontrada para remoção")
		return nil
	}

	log.Printf("[SECURITY] Identificadas %d ordens de teste para remoção: %v", len(finalIDs), finalIDs)

	// Iniciar transação para garantir consistência
	tx := r.db.Begin()

	// 4. Remover registros relacionados primeiro (integridade referencial)
	// Remover notas das ordens
	if err := tx.Where("maintenance_order_id IN (?)", finalIDs).Delete(&models.Note{}).Error; err != nil {
		tx.Rollback()
		log.Printf("[ERROR] Falha ao remover notas de ordens de teste: %v", err)
		return err
	}

	// Remover materiais das ordens
	if err := tx.Where("maintenance_order_id IN (?)", finalIDs).Delete(&models.Material{}).Error; err != nil {
		tx.Rollback()
		log.Printf("[ERROR] Falha ao remover materiais de ordens de teste: %v", err)
		return err
	}

	// Remover fotos das ordens
	if err := tx.Where("order_id IN (?)", finalIDs).Delete(&models.OrderPhoto{}).Error; err != nil {
		tx.Rollback()
		log.Printf("[ERROR] Falha ao remover fotos de ordens de teste: %v", err)
		return err
	}

	// Remover custos das ordens
	if err := tx.Where("order_id IN (?)", finalIDs).Delete(&models.CostItem{}).Error; err != nil {
		tx.Rollback()
		log.Printf("[ERROR] Falha ao remover custos de ordens de teste: %v", err)
		return err
	}

	// Remover registros de técnicos associados
	if err := tx.Table("technician_orders").Where("order_id IN (?)", finalIDs).Delete(nil).Error; err != nil {
		tx.Rollback()
		log.Printf("[ERROR] Falha ao remover registros de técnicos associados a ordens de teste: %v", err)
		return err
	}

	// 5. Finalmente, remover as ordens em si
	if err := tx.Where("id IN (?)", finalIDs).Delete(&models.MaintenanceOrder{}).Error; err != nil {
		tx.Rollback()
		log.Printf("[ERROR] Falha ao remover ordens de teste: %v", err)
		return err
	}

	// Commit da transação
	if err := tx.Commit().Error; err != nil {
		log.Printf("[ERROR] Falha ao finalizar transação de remoção de ordens de teste: %v", err)
		return err
	}

	log.Printf("[SECURITY] %d ordens de teste foram removidas com sucesso", len(finalIDs))
	return nil
}

// GetAllAdminView retorna todas as ordens de manutenção sem filtro para admins, gerentes e financeiros
func (r *MaintenanceOrderRepository) GetAllAdminView(ctx context.Context, page, limit int) ([]models.MaintenanceOrderDetailed, int, error) {
	var orders []models.MaintenanceOrderDetailed
	var total int64

	query := r.db.WithContext(ctx)
	queryCount := r.db.WithContext(ctx)

	// Sem filtro de acesso para administradores
	// Selecionar apenas ordens não deletadas
	query = query.Where("deleted_at IS NULL")
	queryCount = queryCount.Where("deleted_at IS NULL")

	// Tabelas e joins
	tables := []string{TableMaintenanceOrders}
	query = addOrderJoins(query, tables...)
	queryCount = addOrderJoins(queryCount, tables...)

	// Contar total de registros
	err := queryCount.Table(TableMaintenanceOrders).Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("erro ao contar ordens: %v", err)
	}

	// Log para debug
	log.Printf("[DEBUG] Total de ordens para admin view: %d", total)

	// Aplicar paginação
	if page > 0 && limit > 0 {
		offset := (page - 1) * limit
		query = query.Offset(offset).Limit(limit)
	}

	// Aplicar ordenação
	query = query.Order("created_at DESC")

	// Executar a consulta
	rows, err := query.Table(TableMaintenanceOrders).Rows()
	if err != nil {
		return nil, 0, fmt.Errorf("erro ao buscar ordens: %v", err)
	}
	defer rows.Close()

	// Mapear resultados
	for rows.Next() {
		var order models.MaintenanceOrderDetailed
		err := query.ScanRows(rows, &order)
		if err != nil {
			return nil, 0, fmt.Errorf("erro ao mapear ordem: %v", err)
		}
		orders = append(orders, order)
	}

	return orders, int(total), nil
}

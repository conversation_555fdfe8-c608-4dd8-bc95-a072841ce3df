package models

import "time"

// MaintenanceOrderDetailed é um modelo expandido para detalhar completamente uma ordem de manutenção
// Este modelo adiciona campos que são obtidos através de relações ou cálculos
type MaintenanceOrderDetailed struct {
	// Campos básicos da ordem
	ID             uint       `json:"id"`
	OrderNumber    string     `json:"order_number" gorm:"column:number"`
	Title          string     `json:"title"`
	Description    string     `json:"description"`
	Status         string     `json:"status"`
	Priority       string     `json:"priority"`
	Type           string     `json:"type"`
	StationID      uint       `json:"station_id"`
	StationName    string     `json:"station_name"`
	CreatedBy      uint       `json:"created_by"`
	CreatedByName  string     `json:"created_by_name"`
	AssignedTo     *uint      `json:"assigned_to"`
	AssignedToName string     `json:"assigned_to_name,omitempty"`
	StartDate      *time.Time `json:"start_date"`
	CompletedDate  *time.Time `json:"completed_date"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
	Equipment      string     `json:"equipment,omitempty"`

	// Campos adicionais
	TotalCost     float64      `json:"total_cost,omitempty"`
	EstimatedTime *int         `json:"estimated_time,omitempty"`
	ActualTime    *int         `json:"actual_time,omitempty"`
	Notes         []Note       `json:"notes,omitempty" gorm:"-"`     // Usando gorm:"-" para ignorar este campo no mapeamento ORM
	Materials     []Material   `json:"materials,omitempty" gorm:"-"` // Usando gorm:"-" para ignorar este campo no mapeamento ORM
	Photos        []OrderPhoto `json:"photos,omitempty" gorm:"-"`    // Usando gorm:"-" para ignorar este campo no mapeamento ORM
}
